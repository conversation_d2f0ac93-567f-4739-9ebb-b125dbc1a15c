{"name": "bo1", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "clean": "rm -rf dist", "test": "echo \"No tests yet\" && exit 0", "setup:sepolia": "node setup-sepolia.js", "test:config": "npx tsc && node test-dex-config.js", "test:flashbots": "node test-flashbots-integration.js", "validate:apis": "node validate-flashloan-apis.js", "validate:curve": "node validate-curve-strategy.js", "compile": "npx hardhat compile"}, "dependencies": {"@flashbots/ethers-provider-bundle": "^1.0.0", "@flashbots/mev-share-client": "^1.0.0", "@uniswap/sdk-core": "^7.7.2", "@uniswap/v2-sdk": "^4.15.2", "@uniswap/v3-sdk": "^3.25.2", "axios": "^1.9.0", "big.js": "^7.0.1", "chalk": "^4.1.2", "ethers": "^6.7.1", "node-fetch": "^3.3.2", "winston": "^3.17.0", "ws": "^8.18.2"}, "devDependencies": {"@aave/core-v3": "^1.19.3", "@balancer-labs/v2-interfaces": "^0.4.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.9", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-ignition": "^0.15.11", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.12", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@nomicfoundation/ignition-core": "^0.15.11", "@openzeppelin/contracts": "^5.3.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.20", "@types/mocha": "^10.0.10", "@types/node": "^22.15.29", "@types/ws": "^8.18.1", "chai": "^4.5.0", "dotenv": "^16.5.0", "hardhat": "^2.24.3", "hardhat-gas-reporter": "^1.0.10", "nodemon": "^3.1.10", "solidity-coverage": "^0.8.16", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.5.3"}, "private": true}