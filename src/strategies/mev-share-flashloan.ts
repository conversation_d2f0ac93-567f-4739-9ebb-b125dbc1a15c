import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute } from '../types';
import { MEVShareEventMonitor, BackrunOpportunity } from '../mev-share/event-monitor';
import { BalancerFlashloanStrategy } from './balancer-flashloan';
import { FlashbotsBundleManager } from '../flashbots/bundle-provider';
import { FlashbotsBundleTransaction } from '@flashbots/ethers-provider-bundle';
import { config } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';

export interface MEVShareFlashloanRoute extends FlashloanRoute {
  userTxHash: string;
  backrunOpportunity: BackrunOpportunity;
  bundleTransactions: FlashbotsBundleTransaction[];
}

/**
 * MEV-Share Enhanced Flashloan Strategy
 * Combines Balancer flashloans with MEV-Share backrun opportunities
 */
export class MEVShareFlashloanStrategy extends BalancerFlashloanStrategy {
  private mevShareMonitor: MEVShareEventMonitor;
  private flashbotsManager: FlashbotsBundleManager;
  private readonly MIN_GAS_PROTECTION: bigint;
  private readonly MAX_GAS_COST_ETH: number;

  constructor(
    provider: ethers.Provider,
    mevShareMonitor: MEVShareEventMonitor,
    flashbotsManager: FlashbotsBundleManager
  ) {
    super(provider);
    this.mevShareMonitor = mevShareMonitor;
    this.flashbotsManager = flashbotsManager;
    
    // Gas protection settings
    const isMainnet = config.chainId === 1;
    this.MIN_GAS_PROTECTION = isMainnet 
      ? ethers.parseEther('0.005')  // 0.005 ETH minimum profit after gas
      : ethers.parseEther('0.001'); // 0.001 ETH on testnet
    
    this.MAX_GAS_COST_ETH = isMainnet ? 0.02 : 0.005; // Maximum gas cost allowed

    this.setupEventListeners();
    enhancedLogger.systemStatus('🔄 MEV-Share Flashloan Strategy initialized');
    enhancedLogger.systemStatus(`   Gas Protection: ${ethers.formatEther(this.MIN_GAS_PROTECTION)} ETH`);
    enhancedLogger.systemStatus(`   Max Gas Cost: ${this.MAX_GAS_COST_ETH} ETH`);
  }

  /**
   * Setup MEV-Share event listeners
   */
  private setupEventListeners(): void {
    this.mevShareMonitor.on('backrunOpportunity', async (opportunity: BackrunOpportunity) => {
      await this.handleBackrunOpportunity(opportunity);
    });
  }

  /**
   * Handle detected backrun opportunity
   */
  private async handleBackrunOpportunity(opportunity: BackrunOpportunity): Promise<void> {
    try {
      enhancedLogger.separator();
      enhancedLogger.systemStatus('🎯 MEV-Share Backrun Opportunity Detected');
      enhancedLogger.transactionHash(opportunity.userTxHash, 'User transaction');
      enhancedLogger.profitCalculation(ethers.formatEther(opportunity.estimatedProfit), true);
      enhancedLogger.systemStatus(`Confidence: ${opportunity.confidence}%`);

      // Check gas protection
      if (!this.passesGasProtection(opportunity)) {
        enhancedLogger.systemStatus('❌ Opportunity rejected: Gas protection threshold not met');
        return;
      }

      // Create MEV-Share flashloan route
      const mevShareRoute = await this.createMEVShareFlashloanRoute(opportunity);
      if (!mevShareRoute) {
        enhancedLogger.systemStatus('❌ Failed to create MEV-Share flashloan route');
        return;
      }

      // Execute via Flashbots bundle
      const success = await this.executeMEVShareFlashloan(mevShareRoute);
      if (success) {
        enhancedLogger.success('✅ MEV-Share flashloan executed successfully');
      } else {
        enhancedLogger.systemStatus('❌ MEV-Share flashloan execution failed');
      }

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.handleBackrunOpportunity');
      enhancedLogger.error('MEV-Share opportunity handling failed', error);
    }
  }

  /**
   * Check if opportunity passes gas protection threshold
   */
  private passesGasProtection(opportunity: BackrunOpportunity): boolean {
    const gasEstimateEth = Number(ethers.formatEther(opportunity.gasEstimate));
    const profitAfterGas = opportunity.estimatedProfit - opportunity.gasEstimate;

    // Check maximum gas cost
    if (gasEstimateEth > this.MAX_GAS_COST_ETH) {
      enhancedLogger.systemStatus(`   Gas cost too high: ${gasEstimateEth.toFixed(4)} ETH > ${this.MAX_GAS_COST_ETH} ETH`);
      return false;
    }

    // Check minimum profit after gas
    if (profitAfterGas < this.MIN_GAS_PROTECTION) {
      enhancedLogger.systemStatus(`   Profit after gas too low: ${ethers.formatEther(profitAfterGas)} ETH`);
      return false;
    }

    // Check confidence threshold
    if (opportunity.confidence < 70) {
      enhancedLogger.systemStatus(`   Confidence too low: ${opportunity.confidence}%`);
      return false;
    }

    enhancedLogger.systemStatus('✅ Gas protection checks passed');
    enhancedLogger.systemStatus(`   Estimated Gas: ${gasEstimateEth.toFixed(4)} ETH`);
    enhancedLogger.systemStatus(`   Profit After Gas: ${ethers.formatEther(profitAfterGas)} ETH`);
    
    return true;
  }

  /**
   * Create MEV-Share flashloan route
   */
  private async createMEVShareFlashloanRoute(
    opportunity: BackrunOpportunity
  ): Promise<MEVShareFlashloanRoute | null> {
    try {
      // First, create a regular flashloan route based on the opportunity
      const flashloanRoutes = await this.scanForBalancerFlashloanOpportunities();
      
      if (flashloanRoutes.length === 0) {
        enhancedLogger.systemStatus('❌ No flashloan opportunities found');
        return null;
      }

      // Select the best route
      const bestRoute = flashloanRoutes[0];

      // Create bundle transactions
      const bundleTransactions = await this.createBundleTransactions(bestRoute, opportunity);

      const mevShareRoute: MEVShareFlashloanRoute = {
        ...bestRoute,
        userTxHash: opportunity.userTxHash,
        backrunOpportunity: opportunity,
        bundleTransactions
      };

      enhancedLogger.systemStatus('✅ MEV-Share flashloan route created');
      enhancedLogger.systemStatus(`   Bundle transactions: ${bundleTransactions.length}`);

      return mevShareRoute;

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.createMEVShareFlashloanRoute');
      return null;
    }
  }

  /**
   * Create bundle transactions for MEV-Share execution
   */
  private async createBundleTransactions(
    route: FlashloanRoute,
    opportunity: BackrunOpportunity
  ): Promise<FlashbotsBundleTransaction[]> {
    const transactions: FlashbotsBundleTransaction[] = [];

    try {
      // Create flashloan transaction
      const flashloanTx = await this.createFlashloanTransaction(route);
      if (flashloanTx) {
        transactions.push(flashloanTx);
      }

      enhancedLogger.systemStatus(`✅ Created ${transactions.length} bundle transactions`);
      return transactions;

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.createBundleTransactions');
      return [];
    }
  }

  /**
   * Create flashloan transaction for bundle
   */
  private async createFlashloanTransaction(route: FlashloanRoute): Promise<FlashbotsBundleTransaction | null> {
    try {
      if (!this.balancerContract) {
        enhancedLogger.systemStatus('❌ Balancer contract not available');
        return null;
      }

      // Prepare transaction data
      const tokens = [route.flashloanToken.address];
      const amounts = [route.flashloanAmount];

      // Encode arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'uint256'],
        [
          route.flashloanToken.address,
          route.arbitrageRoute.tokens[1].address,
          route.expectedProfit
        ]
      );

      // Create transaction request
      const txRequest = await this.balancerContract.executeFlashloanArbitrage.populateTransaction(
        tokens,
        amounts,
        arbitrageParams
      );

      // Add gas settings
      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      txRequest.gasLimit = BigInt(400000); // Conservative gas limit
      txRequest.maxFeePerGas = gasStrategy.maxFeePerGas;
      txRequest.maxPriorityFeePerGas = gasStrategy.maxPriorityFeePerGas;

      return this.flashbotsManager.createBundleTransaction(txRequest);

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.createFlashloanTransaction');
      return null;
    }
  }

  /**
   * Execute MEV-Share flashloan via Flashbots bundle
   */
  private async executeMEVShareFlashloan(route: MEVShareFlashloanRoute): Promise<boolean> {
    try {
      enhancedLogger.separator();
      enhancedLogger.systemStatus('📦 Executing MEV-Share Flashloan Bundle');
      enhancedLogger.systemStatus(`User Tx: ${route.userTxHash}`);
      enhancedLogger.systemStatus(`Bundle Size: ${route.bundleTransactions.length} transactions`);
      enhancedLogger.profitCalculation(ethers.formatEther(route.expectedProfit), true);

      if (config.dryRun) {
        enhancedLogger.systemStatus('DRY RUN: Simulating MEV-Share bundle execution...');
        
        enhancedLogger.systemStatus('Step 1: 📦 Create Bundle');
        enhancedLogger.systemStatus(`  └─ User Transaction: ${route.userTxHash}`);
        enhancedLogger.systemStatus(`  └─ Backrun Transaction: Balancer Flashloan`);
        
        enhancedLogger.systemStatus('Step 2: 🔍 Simulate Bundle');
        enhancedLogger.systemStatus(`  └─ Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
        enhancedLogger.systemStatus(`  └─ Gas Cost: ${ethers.formatEther(route.gasEstimate)} ETH`);
        
        enhancedLogger.systemStatus('Step 3: 📡 Submit to Flashbots');
        enhancedLogger.systemStatus(`  └─ Bundle Hash: 0x${Math.random().toString(16).slice(2, 18)}...`);
        
        enhancedLogger.success('✅ MEV-Share bundle simulation completed');
        enhancedLogger.separator();
        return true;
      }

      // Get target block
      const currentBlock = await this.flashbotsManager.getCurrentBlock();
      const targetBlock = currentBlock + 1;

      // Create bundle with user transaction hash and our backrun
      const bundleTransactions = [
        { hash: route.userTxHash }, // User transaction (by hash)
        ...route.bundleTransactions  // Our backrun transactions
      ];

      // Simulate bundle first
      const simulation = await this.flashbotsManager.simulateBundle(
        bundleTransactions,
        targetBlock
      );

      if (!simulation.success) {
        enhancedLogger.systemStatus('❌ Bundle simulation failed');
        enhancedLogger.systemStatus(`   Error: ${simulation.error}`);
        return false;
      }

      enhancedLogger.systemStatus('✅ Bundle simulation successful');

      // Submit bundle
      const submission = await this.flashbotsManager.submitBundle(
        bundleTransactions,
        targetBlock
      );

      if (submission.success) {
        enhancedLogger.systemStatus('🎉 Bundle submitted successfully');
        enhancedLogger.systemStatus(`   Bundle Hash: ${submission.bundleHash}`);
        return true;
      } else {
        enhancedLogger.systemStatus('❌ Bundle submission failed');
        enhancedLogger.systemStatus(`   Error: ${submission.error}`);
        return false;
      }

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.executeMEVShareFlashloan');
      enhancedLogger.error('MEV-Share flashloan execution failed', error);
      return false;
    }
  }

  /**
   * Start MEV-Share monitoring
   */
  async startMonitoring(): Promise<void> {
    try {
      await this.mevShareMonitor.initialize();
      await this.mevShareMonitor.start();
      enhancedLogger.systemStatus('🔄 MEV-Share flashloan monitoring started');
    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.startMonitoring');
      enhancedLogger.systemStatus('❌ Failed to start MEV-Share monitoring');
    }
  }

  /**
   * Stop MEV-Share monitoring
   */
  async stopMonitoring(): Promise<void> {
    try {
      await this.mevShareMonitor.stop();
      enhancedLogger.systemStatus('🛑 MEV-Share flashloan monitoring stopped');
    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.stopMonitoring');
    }
  }

  /**
   * Get strategy status
   */
  getStatus(): {
    mevShareAvailable: boolean;
    monitoring: boolean;
    gasProtection: string;
    maxGasCost: string;
  } {
    const mevShareStatus = this.mevShareMonitor.getStatus();
    
    return {
      mevShareAvailable: mevShareStatus.isAvailable,
      monitoring: mevShareStatus.isRunning,
      gasProtection: ethers.formatEther(this.MIN_GAS_PROTECTION) + ' ETH',
      maxGasCost: this.MAX_GAS_COST_ETH + ' ETH'
    };
  }
}
