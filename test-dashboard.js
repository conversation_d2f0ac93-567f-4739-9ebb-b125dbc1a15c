#!/usr/bin/env node

/**
 * Test the new Status Dashboard
 * Shows how the clean dashboard replaces spammy transaction logs
 */

const { statusDashboard } = require('./dist/utils/statusDashboard.js');
const { ethers } = require('ethers');

console.log('🔍 Testing Status Dashboard vs Spammy Logs\n');

// Simulate the old spammy way
console.log('❌ OLD WAY (Spammy):');
console.log('─'.repeat(50));

for (let i = 0; i < 10; i++) {
  const txHash = `0x${Math.random().toString(16).slice(2, 18)}...`;
  console.log(`[${new Date().toISOString()}] Transaction Hash = ${txHash} Pending transaction detected`);
}

console.log('\n💥 This would continue flooding your console!\n');

// Now show the new dashboard way
console.log('✅ NEW WAY (Clean Dashboard):');
console.log('─'.repeat(50));

// Initialize dashboard with realistic Sepolia block number
const currentSepoliaBlock = 5000000 + Math.floor(Date.now() / 12000); // Simulate ~12 second blocks
statusDashboard.updateNetworkStatus(currentSepoliaBlock, 'Sepolia');
statusDashboard.updateStrategyStatus(true, false, false); // Flashloan enabled, MEV-Share disabled, Arbitrage disabled

// Simulate some activity
console.log('Simulating MEV bot activity...\n');

// Simulate transactions
for (let i = 0; i < 50; i++) {
  const isRelevant = Math.random() > 0.8; // 20% relevant
  const gasPrice = ethers.parseUnits((20 + Math.random() * 30).toFixed(0), 'gwei');
  statusDashboard.recordTransaction(isRelevant, gasPrice);
}

// Simulate some successful transactions
const txTypes = ['flashloan', 'arbitrage', 'sandwich', 'mev-share'];
for (let i = 0; i < 8; i++) {
  const profit = ethers.parseEther((Math.random() * 0.05 + 0.001).toFixed(4));
  const gasUsed = ethers.parseEther((Math.random() * 0.01 + 0.001).toFixed(4));
  const type = txTypes[Math.floor(Math.random() * txTypes.length)];
  const confidence = Math.floor(Math.random() * 30 + 70); // 70-100%

  statusDashboard.recordSuccessfulTransaction({
    timestamp: Date.now() - (i * 30000), // Spread over last few minutes
    type: type,
    profit: profit,
    gasUsed: gasUsed,
    txHash: `0x${Math.random().toString(16).slice(2, 18)}${'0'.repeat(40)}`,
    bundleHash: Math.random() > 0.5 ? `0x${Math.random().toString(16).slice(2, 18)}${'0'.repeat(40)}` : undefined,
    confidence: confidence,
    details: type === 'flashloan' ? 'USDC → DAI → USDT' :
             type === 'arbitrage' ? 'Uniswap V2 → V3' :
             type === 'sandwich' ? 'Front+Back run' :
             'Backrun opportunity'
  });
}

// Simulate some errors
statusDashboard.recordError('Rate limit exceeded');
statusDashboard.recordError('Insufficient gas');

// Start the dashboard
statusDashboard.start();

console.log('Dashboard started! You should see a clean, updating status instead of spam.\n');
console.log('Press Ctrl+C to stop the test.\n');

// Keep the process running
process.on('SIGINT', () => {
  statusDashboard.stop();
  console.log('\n\n✅ Dashboard test completed!');
  console.log('\nBenefits of the new dashboard:');
  console.log('• No more spammy "Pending transaction detected" messages');
  console.log('• Clean, organized status overview');
  console.log('• Real-time statistics and metrics');
  console.log('• Easy to monitor bot performance');
  console.log('• Automatic updates every 5 seconds');
  console.log('• Shows only relevant information');
  process.exit(0);
});

// Simulate ongoing activity
setInterval(() => {
  // Add more transactions
  for (let i = 0; i < 5; i++) {
    const isRelevant = Math.random() > 0.85;
    const gasPrice = ethers.parseUnits((15 + Math.random() * 40).toFixed(0), 'gwei');
    statusDashboard.recordTransaction(isRelevant, gasPrice);
  }
  
  // Occasionally add successful transactions
  if (Math.random() > 0.8) {
    const profit = ethers.parseEther((Math.random() * 0.03 + 0.001).toFixed(4));
    const gasUsed = ethers.parseEther((Math.random() * 0.008 + 0.001).toFixed(4));
    const types = ['flashloan', 'arbitrage', 'mev-share'];
    const type = types[Math.floor(Math.random() * types.length)];

    statusDashboard.recordSuccessfulTransaction({
      timestamp: Date.now(),
      type: type,
      profit: profit,
      gasUsed: gasUsed,
      txHash: `0x${Math.random().toString(16).slice(2, 18)}${'0'.repeat(40)}`,
      bundleHash: type === 'mev-share' ? `0x${Math.random().toString(16).slice(2, 18)}${'0'.repeat(40)}` : undefined,
      confidence: Math.floor(Math.random() * 25 + 75),
      details: type === 'flashloan' ? 'USDC → WETH' :
               type === 'arbitrage' ? 'Cross-DEX' :
               'Live backrun'
    });
  }
  
  // Update block number (simulate realistic Sepolia blocks)
  const currentBlock = 5000000 + Math.floor(Date.now() / 12000); // ~12 second blocks
  statusDashboard.updateNetworkStatus(currentBlock, 'Sepolia');
}, 2000);
